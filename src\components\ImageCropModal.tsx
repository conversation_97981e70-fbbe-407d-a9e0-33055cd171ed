import React, { useState, useRef, useCallback } from "react";
import <PERSON>actCrop, {
  Crop,
  PixelCrop,
  centerCrop,
  makeAspectCrop,
} from "react-image-crop";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X, Upload, Crop as CropIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import "react-image-crop/dist/ReactCrop.css";

// Custom styles for the slider
const sliderStyles = `
  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #16a34a;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  }
  .slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #16a34a;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  }
`;

interface ImageCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCropComplete: (croppedImageUrl: string) => void;
}

const ImageCropModal = ({
  isOpen,
  onClose,
  onCropComplete,
}: ImageCropModalProps) => {
  const { toast } = useToast();
  const [imgSrc, setImgSrc] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  const onSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Invalid File Type",
          description: "Please select an image file.",
          variant: "destructive",
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive",
        });
        return;
      }

      setCrop(undefined); // Makes crop preview update between images.
      const reader = new FileReader();
      reader.addEventListener("load", () =>
        setImgSrc(reader.result?.toString() || "")
      );
      reader.readAsDataURL(file);
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;

    // Create a centered crop with 1:1 aspect ratio
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: "%",
          width: 90,
        },
        1, // aspect ratio 1:1 for profile photos
        width,
        height
      ),
      width,
      height
    );

    setCrop(crop);
  };

  const getCroppedImg = useCallback(
    async (image: HTMLImageElement, crop: PixelCrop, scale = 1, rotate = 0) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        throw new Error("No 2d context");
      }

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;
      const pixelRatio = window.devicePixelRatio;

      canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
      canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

      ctx.scale(pixelRatio, pixelRatio);
      ctx.imageSmoothingQuality = "high";

      const cropX = crop.x * scaleX;
      const cropY = crop.y * scaleY;

      const rotateRads = rotate * (Math.PI / 180);
      const centerX = image.naturalWidth / 2;
      const centerY = image.naturalHeight / 2;

      ctx.save();

      // 5) Move the crop origin to the canvas origin (0,0)
      ctx.translate(-cropX, -cropY);
      // 4) Move the origin to the center of the original position
      ctx.translate(centerX, centerY);
      // 3) Rotate around the origin
      ctx.rotate(rotateRads);
      // 2) Scale the image
      ctx.scale(scale, scale);
      // 1) Move the center of the image to the origin (0,0)
      ctx.translate(-centerX, -centerY);
      ctx.drawImage(image, 0, 0);
      ctx.restore();

      return new Promise<string>((resolve) => {
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              throw new Error("Failed to create blob");
            }
            const croppedImageUrl = URL.createObjectURL(blob);
            resolve(croppedImageUrl);
          },
          "image/jpeg",
          0.9
        );
      });
    },
    []
  );

  const handleCropAndUpload = useCallback(async () => {
    if (completedCrop?.width && completedCrop?.height && imgRef.current) {
      setIsProcessing(true);
      try {
        const croppedImageUrl = await getCroppedImg(
          imgRef.current,
          completedCrop,
          scale,
          rotate
        );

        // Convert to base64 for storage
        const response = await fetch(croppedImageUrl);
        const blob = await response.blob();
        const reader = new FileReader();
        reader.onload = () => {
          const base64String = reader.result as string;
          onCropComplete(base64String);
          handleClose();
          toast({
            title: "Profile Photo Updated",
            description:
              "Your profile photo has been successfully updated and will appear across all sections.",
          });
          setIsProcessing(false);
        };
        reader.readAsDataURL(blob);

        // Clean up the blob URL
        URL.revokeObjectURL(croppedImageUrl);
      } catch (error) {
        console.error("Error cropping image:", error);
        toast({
          title: "Error",
          description: "Failed to crop image. Please try again.",
          variant: "destructive",
        });
        setIsProcessing(false);
      }
    }
  }, [completedCrop, scale, rotate, getCroppedImg, onCropComplete, toast]);

  const handleClose = () => {
    setImgSrc("");
    setCrop(undefined);
    setCompletedCrop(undefined);
    setScale(1);
    setRotate(0);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <style dangerouslySetInnerHTML={{ __html: sliderStyles }} />
      <DialogContent className="fixed inset-0 w-screen h-screen max-w-none max-h-none p-0 m-0 bg-white dark:bg-gray-900 transition-all duration-300 ease-in-out z-50">
        {/* Full-screen Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
          <div className="flex flex-col">
            <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Crop Profile Photo
            </DialogTitle>
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                📸 Drag to reposition • 🔍 Use slider to zoom • ✨ Click "Save
                Photo" to apply
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                Your photo will appear in Profile Page, Navbar, and Staff Table
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-300 ease-in-out rounded-full p-3"
            title="Close"
          >
            <X className="w-6 h-6" />
          </Button>
        </div>

        {/* Full-screen Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {!imgSrc && (
            <div className="flex-1 flex items-center justify-center p-6">
              <div className="text-center space-y-6 max-w-md">
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-12 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors">
                  <Upload className="w-16 h-16 mx-auto text-gray-400 mb-6" />
                  <p className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    Select an image to crop
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                    Choose a photo for your profile. Max file size: 5MB
                  </p>
                  <Button
                    onClick={() =>
                      document.getElementById("crop-file-input")?.click()
                    }
                    className="px-8 py-3 text-lg transition-all duration-300 ease-in-out"
                    size="lg"
                  >
                    <Upload className="w-5 h-5 mr-3" />
                    Choose Image
                  </Button>
                  <input
                    id="crop-file-input"
                    type="file"
                    accept="image/*"
                    onChange={onSelectFile}
                    className="hidden"
                  />
                </div>
              </div>
            </div>
          )}

          {imgSrc && (
            <div className="flex-1 flex flex-col p-6 space-y-6">
              {/* Crop Area - Takes most of the screen */}
              <div className="flex-1 flex items-center justify-center min-h-0">
                <div className="relative w-full h-full max-w-4xl max-h-[70vh] border-2 border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-800 overflow-hidden shadow-lg">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={1}
                    minWidth={100}
                    minHeight={100}
                    circularCrop
                    className="w-full h-full"
                  >
                    <img
                      ref={imgRef}
                      alt="Crop me"
                      src={imgSrc}
                      style={{
                        transform: `scale(${scale}) rotate(${rotate}deg)`,
                      }}
                      onLoad={onImageLoad}
                      className="block w-full h-full object-contain"
                    />
                  </ReactCrop>
                </div>
              </div>

              {/* Zoom Controls */}
              <div className="flex items-center justify-center gap-6 py-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
                <label className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                  🔍 Zoom:
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="3"
                  step="0.1"
                  value={scale}
                  onChange={(e) => setScale(Number(e.target.value))}
                  className="flex-1 max-w-md h-3 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                />
                <span className="text-lg font-bold text-gray-900 dark:text-white min-w-[4rem] bg-white dark:bg-gray-700 px-3 py-1 rounded-lg border">
                  {Math.round(scale * 100)}%
                </span>
              </div>

              {/* Action Buttons - Fixed at bottom */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t-2 border-gray-200 dark:border-gray-700">
                {/* Left side - Choose Different Image */}
                <Button
                  variant="outline"
                  onClick={() =>
                    document.getElementById("crop-file-input")?.click()
                  }
                  className="flex-1 sm:flex-none px-6 py-3 text-lg font-medium transition-all duration-300 ease-in-out border-2 hover:border-gray-400 dark:hover:border-gray-500"
                  size="lg"
                >
                  <Upload className="w-5 h-5 mr-3" />
                  Choose Different Image
                </Button>

                {/* Center - Primary Save Button */}
                <Button
                  onClick={handleCropAndUpload}
                  disabled={
                    !completedCrop?.width ||
                    !completedCrop?.height ||
                    isProcessing
                  }
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white transition-all duration-300 ease-in-out py-4 text-xl font-bold shadow-xl hover:shadow-2xl transform hover:scale-105 disabled:transform-none"
                  size="lg"
                >
                  {isProcessing ? (
                    <>
                      <div className="w-6 h-6 mr-3 border-3 border-white border-t-transparent rounded-full animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CropIcon className="w-6 h-6 mr-3" />✨ Save Photo
                    </>
                  )}
                </Button>

                {/* Right side - Cancel */}
                <Button
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1 sm:flex-none px-6 py-3 text-lg font-medium transition-all duration-300 ease-in-out border-2 hover:border-red-400 hover:text-red-600 dark:hover:border-red-500 dark:hover:text-red-400"
                  size="lg"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageCropModal;
