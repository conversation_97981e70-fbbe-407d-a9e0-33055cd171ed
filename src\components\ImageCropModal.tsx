import React, { useState, useRef, useCallback } from "react";
import <PERSON>actCrop, {
  Crop,
  PixelCrop,
  centerCrop,
  makeAspectCrop,
} from "react-image-crop";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, Upload, Crop as CropIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import "react-image-crop/dist/ReactCrop.css";

interface ImageCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCropComplete: (croppedImageUrl: string) => void;
}

const ImageCropModal = ({
  isOpen,
  onClose,
  onCropComplete,
}: ImageCropModalProps) => {
  const { toast } = useToast();
  const [imgSrc, setImgSrc] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  const onSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Invalid File Type",
          description: "Please select an image file.",
          variant: "destructive",
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive",
        });
        return;
      }

      setCrop(undefined); // Makes crop preview update between images.
      const reader = new FileReader();
      reader.addEventListener("load", () =>
        setImgSrc(reader.result?.toString() || "")
      );
      reader.readAsDataURL(file);
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;

    // Create a centered crop with 1:1 aspect ratio
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: "%",
          width: 90,
        },
        1, // aspect ratio 1:1 for profile photos
        width,
        height
      ),
      width,
      height
    );

    setCrop(crop);
  };

  const getCroppedImg = useCallback(
    async (image: HTMLImageElement, crop: PixelCrop, scale = 1, rotate = 0) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        throw new Error("No 2d context");
      }

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;
      const pixelRatio = window.devicePixelRatio;

      canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
      canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

      ctx.scale(pixelRatio, pixelRatio);
      ctx.imageSmoothingQuality = "high";

      const cropX = crop.x * scaleX;
      const cropY = crop.y * scaleY;

      const rotateRads = rotate * (Math.PI / 180);
      const centerX = image.naturalWidth / 2;
      const centerY = image.naturalHeight / 2;

      ctx.save();

      // 5) Move the crop origin to the canvas origin (0,0)
      ctx.translate(-cropX, -cropY);
      // 4) Move the origin to the center of the original position
      ctx.translate(centerX, centerY);
      // 3) Rotate around the origin
      ctx.rotate(rotateRads);
      // 2) Scale the image
      ctx.scale(scale, scale);
      // 1) Move the center of the image to the origin (0,0)
      ctx.translate(-centerX, -centerY);
      ctx.drawImage(image, 0, 0);
      ctx.restore();

      return new Promise<string>((resolve) => {
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              throw new Error("Failed to create blob");
            }
            const croppedImageUrl = URL.createObjectURL(blob);
            resolve(croppedImageUrl);
          },
          "image/jpeg",
          0.9
        );
      });
    },
    []
  );

  const handleCropAndUpload = useCallback(async () => {
    if (completedCrop?.width && completedCrop?.height && imgRef.current) {
      setIsProcessing(true);
      try {
        const croppedImageUrl = await getCroppedImg(
          imgRef.current,
          completedCrop,
          scale,
          rotate
        );

        // Convert to base64 for storage
        const response = await fetch(croppedImageUrl);
        const blob = await response.blob();
        const reader = new FileReader();
        reader.onload = () => {
          const base64String = reader.result as string;
          onCropComplete(base64String);
          handleClose();
          toast({
            title: "Profile Photo Updated",
            description:
              "Your profile photo has been successfully updated and will appear across all sections.",
          });
          setIsProcessing(false);
        };
        reader.readAsDataURL(blob);

        // Clean up the blob URL
        URL.revokeObjectURL(croppedImageUrl);
      } catch (error) {
        console.error("Error cropping image:", error);
        toast({
          title: "Error",
          description: "Failed to crop image. Please try again.",
          variant: "destructive",
        });
        setIsProcessing(false);
      }
    }
  }, [completedCrop, scale, rotate, getCroppedImg, onCropComplete, toast]);

  const handleClose = () => {
    setImgSrc("");
    setCrop(undefined);
    setCompletedCrop(undefined);
    setScale(1);
    setRotate(0);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden transition-all duration-300 ease-in-out">
        <DialogHeader className="p-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <CropIcon className="w-5 h-5" />
              Crop Profile Photo
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-300 ease-in-out"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="p-6 space-y-4">
          {!imgSrc && (
            <div className="text-center space-y-4">
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 bg-gray-50 dark:bg-gray-800">
                <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select an image to crop
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  Choose a photo for your profile. Max file size: 5MB
                </p>
                <Button
                  onClick={() =>
                    document.getElementById("crop-file-input")?.click()
                  }
                  className="transition-all duration-300 ease-in-out"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Image
                </Button>
                <input
                  id="crop-file-input"
                  type="file"
                  accept="image/*"
                  onChange={onSelectFile}
                  className="hidden"
                />
              </div>
            </div>
          )}

          {imgSrc && (
            <div className="space-y-4">
              <div className="text-center mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Drag to reposition • Use the slider to zoom • Your photo will
                  appear in your profile, navbar, and team list
                </p>
              </div>
              <div className="flex justify-center">
                <div className="relative max-h-[500px] max-w-full overflow-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-900">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={1}
                    minWidth={100}
                    minHeight={100}
                    circularCrop
                    className="max-h-[500px]"
                  >
                    <img
                      ref={imgRef}
                      alt="Crop me"
                      src={imgSrc}
                      style={{
                        transform: `scale(${scale}) rotate(${rotate}deg)`,
                      }}
                      onLoad={onImageLoad}
                      className="block max-w-none"
                    />
                  </ReactCrop>
                </div>
              </div>

              {/* Zoom Controls */}
              <div className="flex items-center justify-center gap-4">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Zoom:
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="3"
                  step="0.1"
                  value={scale}
                  onChange={(e) => setScale(Number(e.target.value))}
                  className="w-32 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[3rem]">
                  {Math.round(scale * 100)}%
                </span>
              </div>

              <div className="flex justify-between items-center pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() =>
                    document.getElementById("crop-file-input")?.click()
                  }
                  className="transition-all duration-300 ease-in-out"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Different Image
                </Button>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={handleClose}
                    className="transition-all duration-300 ease-in-out"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCropAndUpload}
                    disabled={
                      !completedCrop?.width ||
                      !completedCrop?.height ||
                      isProcessing
                    }
                    className="bg-green-600 hover:bg-green-700 text-white transition-all duration-300 ease-in-out px-6"
                  >
                    {isProcessing ? (
                      <>
                        <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <CropIcon className="w-4 h-4 mr-2" />
                        Save Photo
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageCropModal;
