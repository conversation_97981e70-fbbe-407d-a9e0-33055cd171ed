import React, { useState, useRef, useCallback } from "react";
import <PERSON>actCrop, {
  Crop,
  PixelCrop,
  centerCrop,
  makeAspectCrop,
} from "react-image-crop";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X, Upload, Crop as CropIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import "react-image-crop/dist/ReactCrop.css";

// Custom styles for the slider
const sliderStyles = `
  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #16a34a;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  }
  .slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #16a34a;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  }
`;

interface ImageCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCropComplete: (croppedImageUrl: string) => void;
}

const ImageCropModal = ({
  isOpen,
  onClose,
  onCropComplete,
}: ImageCropModalProps) => {
  const { toast } = useToast();
  const [imgSrc, setImgSrc] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  const onSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Invalid File Type",
          description: "Please select an image file.",
          variant: "destructive",
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive",
        });
        return;
      }

      setCrop(undefined); // Makes crop preview update between images.
      const reader = new FileReader();
      reader.addEventListener("load", () =>
        setImgSrc(reader.result?.toString() || "")
      );
      reader.readAsDataURL(file);
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;

    // Create a centered crop with 1:1 aspect ratio
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: "%",
          width: 90,
        },
        1, // aspect ratio 1:1 for profile photos
        width,
        height
      ),
      width,
      height
    );

    setCrop(crop);
  };

  const getCroppedImg = useCallback(
    async (image: HTMLImageElement, crop: PixelCrop, scale = 1, rotate = 0) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        throw new Error("No 2d context");
      }

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;
      const pixelRatio = window.devicePixelRatio;

      canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
      canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

      ctx.scale(pixelRatio, pixelRatio);
      ctx.imageSmoothingQuality = "high";

      const cropX = crop.x * scaleX;
      const cropY = crop.y * scaleY;

      const rotateRads = rotate * (Math.PI / 180);
      const centerX = image.naturalWidth / 2;
      const centerY = image.naturalHeight / 2;

      ctx.save();

      // 5) Move the crop origin to the canvas origin (0,0)
      ctx.translate(-cropX, -cropY);
      // 4) Move the origin to the center of the original position
      ctx.translate(centerX, centerY);
      // 3) Rotate around the origin
      ctx.rotate(rotateRads);
      // 2) Scale the image
      ctx.scale(scale, scale);
      // 1) Move the center of the image to the origin (0,0)
      ctx.translate(-centerX, -centerY);
      ctx.drawImage(image, 0, 0);
      ctx.restore();

      return new Promise<string>((resolve) => {
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              throw new Error("Failed to create blob");
            }
            const croppedImageUrl = URL.createObjectURL(blob);
            resolve(croppedImageUrl);
          },
          "image/jpeg",
          0.9
        );
      });
    },
    []
  );

  const handleCropAndUpload = useCallback(async () => {
    if (completedCrop?.width && completedCrop?.height && imgRef.current) {
      setIsProcessing(true);
      try {
        const croppedImageUrl = await getCroppedImg(
          imgRef.current,
          completedCrop,
          scale,
          rotate
        );

        // Convert to base64 for storage
        const response = await fetch(croppedImageUrl);
        const blob = await response.blob();
        const reader = new FileReader();
        reader.onload = () => {
          const base64String = reader.result as string;
          onCropComplete(base64String);
          handleClose();
          toast({
            title: "Profile Photo Updated",
            description:
              "Your profile photo has been successfully updated and will appear across all sections.",
          });
          setIsProcessing(false);
        };
        reader.readAsDataURL(blob);

        // Clean up the blob URL
        URL.revokeObjectURL(croppedImageUrl);
      } catch (error) {
        console.error("Error cropping image:", error);
        toast({
          title: "Error",
          description: "Failed to crop image. Please try again.",
          variant: "destructive",
        });
        setIsProcessing(false);
      }
    }
  }, [completedCrop, scale, rotate, getCroppedImg, onCropComplete, toast]);

  const handleClose = () => {
    setImgSrc("");
    setCrop(undefined);
    setCompletedCrop(undefined);
    setScale(1);
    setRotate(0);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <style dangerouslySetInnerHTML={{ __html: sliderStyles }} />
      <DialogContent className="fixed inset-0 w-screen h-screen max-w-none max-h-none p-0 m-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        {/* Centered Modal Container */}
        <div className="w-full max-w-6xl h-full max-h-[95vh] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl flex flex-col overflow-hidden mx-4">
          {/* Modal Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-800">
            <div className="flex-1 text-center">
              <DialogTitle className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
                📸 Crop Profile Photo
              </DialogTitle>
              <div className="space-y-2">
                <p className="text-lg text-gray-700 dark:text-gray-300 font-medium">
                  📸 Drag to reposition • 🔍 Use slider to zoom • ✨ Click "Save
                  Photo" to apply
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Your photo will appear in Profile Page, Navbar, and Staff
                  Table
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="absolute top-4 right-4 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 transition-all duration-300 ease-in-out rounded-full p-3"
              title="Close Modal"
            >
              <X className="w-6 h-6" />
            </Button>
          </div>

          {/* Modal Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {!imgSrc && (
              <div className="flex-1 flex items-center justify-center p-8">
                <div className="text-center space-y-8 max-w-lg">
                  <div
                    className="border-3 border-dashed border-blue-300 dark:border-blue-600 rounded-2xl p-16 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-gray-750 dark:hover:to-gray-650 transition-all duration-300 cursor-pointer"
                    onClick={() =>
                      document.getElementById("crop-file-input")?.click()
                    }
                  >
                    <Upload className="w-20 h-20 mx-auto text-blue-500 dark:text-blue-400 mb-8" />
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">
                      Upload Your Profile Photo
                    </p>
                    <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                      Choose a photo for your profile. Max file size: 5MB
                    </p>
                    <Button
                      onClick={() =>
                        document.getElementById("crop-file-input")?.click()
                      }
                      className="px-10 py-4 text-xl font-semibold bg-blue-600 hover:bg-blue-700 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg"
                      size="lg"
                    >
                      <Upload className="w-6 h-6 mr-4" />
                      Choose Image
                    </Button>
                    <input
                      id="crop-file-input"
                      type="file"
                      accept="image/*"
                      onChange={onSelectFile}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            )}

            {imgSrc && (
              <div className="flex-1 flex flex-col p-8 space-y-8">
                {/* Centered Crop Area */}
                <div className="flex-1 flex items-center justify-center min-h-0">
                  <div className="relative w-full h-full max-w-3xl max-h-[60vh] border-4 border-blue-200 dark:border-blue-700 rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 overflow-hidden shadow-2xl">
                    <ReactCrop
                      crop={crop}
                      onChange={(_, percentCrop) => setCrop(percentCrop)}
                      onComplete={(c) => setCompletedCrop(c)}
                      aspect={1}
                      minWidth={100}
                      minHeight={100}
                      circularCrop
                      className="w-full h-full"
                    >
                      <img
                        ref={imgRef}
                        alt="Crop me"
                        src={imgSrc}
                        style={{
                          transform: `scale(${scale}) rotate(${rotate}deg)`,
                        }}
                        onLoad={onImageLoad}
                        className="block w-full h-full object-contain"
                      />
                    </ReactCrop>
                  </div>
                </div>

                {/* Enhanced Zoom Controls */}
                <div className="flex items-center justify-center gap-8 py-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl border border-blue-200 dark:border-blue-700 shadow-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-2xl">🔍</span>
                    </div>
                    <label className="text-xl font-bold text-gray-800 dark:text-gray-200">
                      Zoom:
                    </label>
                  </div>
                  <input
                    type="range"
                    min="0.5"
                    max="3"
                    step="0.1"
                    value={scale}
                    onChange={(e) => setScale(Number(e.target.value))}
                    className="flex-1 max-w-lg h-4 bg-blue-200 dark:bg-blue-800 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex items-center gap-2">
                    <span className="text-xl font-bold text-blue-600 dark:text-blue-400 min-w-[5rem] bg-white dark:bg-gray-800 px-4 py-2 rounded-xl border-2 border-blue-200 dark:border-blue-600 shadow-md">
                      {Math.round(scale * 100)}%
                    </span>
                  </div>
                </div>

                {/* Enhanced Action Buttons */}
                <div className="flex flex-col lg:flex-row gap-6 pt-8 border-t-4 border-gradient-to-r from-blue-200 to-purple-200 dark:from-blue-700 dark:to-purple-700">
                  {/* Choose Different Image */}
                  <Button
                    variant="outline"
                    onClick={() =>
                      document.getElementById("crop-file-input")?.click()
                    }
                    className="flex-1 lg:flex-none px-8 py-4 text-lg font-semibold transition-all duration-300 ease-in-out border-3 border-blue-300 dark:border-blue-600 hover:border-blue-500 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 transform hover:scale-105 shadow-lg"
                    size="lg"
                  >
                    <Upload className="w-6 h-6 mr-3" />
                    🔄 Choose Different Image
                  </Button>

                  {/* Primary Save Button */}
                  <Button
                    onClick={handleCropAndUpload}
                    disabled={
                      !completedCrop?.width ||
                      !completedCrop?.height ||
                      isProcessing
                    }
                    className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-400 disabled:to-gray-500 text-white transition-all duration-300 ease-in-out py-5 text-2xl font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 disabled:transform-none rounded-2xl"
                    size="lg"
                  >
                    {isProcessing ? (
                      <>
                        <div className="w-8 h-8 mr-4 border-4 border-white border-t-transparent rounded-full animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <CropIcon className="w-8 h-8 mr-4" />
                        Save Photo
                      </>
                    )}
                  </Button>

                  {/* Cancel Button */}
                  <Button
                    variant="outline"
                    onClick={handleClose}
                    className="flex-1 lg:flex-none px-8 py-4 text-lg font-semibold transition-all duration-300 ease-in-out border-3 border-red-300 dark:border-red-600 hover:border-red-500 dark:hover:border-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 transform hover:scale-105 shadow-lg"
                    size="lg"
                  >
                    ❌ Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageCropModal;
